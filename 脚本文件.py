import json
from concurrent import futures
import os
from functools import partial
from typing import Dict

from src.config.log import logger
from src.config import (
    PROCESS_NUMBER,
    SCRIPT_OUTPUT,
    DATA_DIR,
    TODAY_STR,
    S3_UPLOAD_DIR,
    UPLOAD_RECORD_ID,
)
from src.config.exceptions import ScriptExecutionException

from utils.api_utils import (
    get_dataset_info,
    update_dataset_info,
)
from utils.decorators_utils import funciton_handler
from utils.progress_utils import p_bar
from utils.s3_utils import s3_uplod_file


# 默认配置
DEFAULT_RUNTIME_CONFIG = {
    "enable_multiprocessing": False,
    "local_module_name": "custom_libs",
    "mirror": None,
    "data_dir": None,
}


class ScriptRet:
    """
    脚本执行器
    """

    zipinfo_script_mapping: Dict
    unzip_dir: str
    custom_script_attachment_url: str

    def __init__(self):
        self.zipinfo_script_mapping = {}
        self.unzip_dir = ""
        self.custom_script_attachment_url = ""

    @property
    def valid(self) -> bool:
        zipinfo = len(self.zipinfo_script_mapping) == 0
        return zipinfo


@funciton_handler("线上导入脚本", capture_exception=True)
def handle_script(
    upload_record_info: Dict,
    zipinfo_script_mapping: Dict,
) -> ScriptRet:
    script_ret = ScriptRet()

    import script as custom_module  # type: ignore

    script_runtime_config = zipinfo_script_mapping.get("script_runtime_config")

    if script_runtime_config.get("enable_multiprocessing", False):
        progress_pool = futures.ProcessPoolExecutor(max_workers=PROCESS_NUMBER)
    else:
        progress_pool = None
    script_runtime_config["cpu_number"] = PROCESS_NUMBER

    script_success = True

    script_runtime_config["output_dir"] = SCRIPT_OUTPUT

    print("下载的数据:", DATA_DIR)
    print("附件数据", script_runtime_config["data_dir"])
    print("配置:", script_runtime_config)

    # try:
    print("执行脚本")
    custom_module.main(
        DATA_DIR,  # 下载的数据
        script_runtime_config["data_dir"],  # 附件文件中的数据
        SCRIPT_OUTPUT,  # 输出目录
        partial(p_bar.update_script, 100),
        progress_pool,
        script_runtime_config,
    )
    # except Exception:
    #     logger.exception("执行自定义脚本失败")
    #     script_success = False
    # finally:
    #     if progress_pool is not None:
    #         progress_pool.shutdown(wait=True)

    if not script_success:
        raise ScriptExecutionException("执行自定义脚本失败")

    json_result_dir = SCRIPT_OUTPUT / "json"
    print("脚本处理结果地址11:", json_result_dir)
    if json_result_dir.exists():
        print("json模式")
        for json_result_path in SCRIPT_OUTPUT.rglob("*.json"):
            print("json_result_pathxxxxxx:", json_result_path)
            script_ret.zipinfo_script_mapping[json_result_path.stem] = {
                "json_path": str(json_result_path),
                "dataset_id": upload_record_info["datasetId"],
                "upload_type": upload_record_info["uploadType"],
                "upload_mode_type": "CUSTOM",
                "create_time": upload_record_info["createTime"],
            }

    zip_result_dir = SCRIPT_OUTPUT / "zip"

    print("脚本处理结果地址22:", zip_result_dir)

    if zip_result_dir.exists():
        print("存在")
        script_ret.unzip_dir = str(zip_result_dir.absolute())

        for zip_result in zip_result_dir.iterdir():
            if not zip_result.is_dir():
                os.remove(zip_result)
                continue
            print("save some data")
            script_ret.zipinfo_script_mapping[zip_result.stem] = {
                "un_zip_save_dir": str(zip_result.absolute()),
                "dataset_id": upload_record_info["datasetId"],
                "upload_type": upload_record_info["uploadType"],
                "upload_mode_type": "CUSTOM",
                "create_time": upload_record_info["createTime"],
            }

    extra_result_dir = SCRIPT_OUTPUT / "extra"

    upload_dir = os.path.join(S3_UPLOAD_DIR, TODAY_STR, str(UPLOAD_RECORD_ID), "extra")

    if extra_result_dir.exists():
        upload_msg = {}

        for file_path in extra_result_dir.rglob("*.*"):
            file_key = file_path.relative_to(extra_result_dir).as_posix()
            upload_msg[file_key] = str(file_path)
        try:
            upload_msg = s3_uplod_file(upload_msg, upload_dir)
        except Exception as e:
            error_msg = f"上传脚本生成数据出错 {e}"
            raise Exception(error_msg)
        else:
            upload_msg_value = json.dumps(upload_msg)

            dataset_info = get_dataset_info(upload_record_info["datasetId"])

            script_ret.custom_script_attachment_url = upload_msg.get(
                "custom_script_attachment.zip", None
            )

            logger.debug(f"附件处理信息{script_ret.custom_script_attachment_url}")

            dataset_info.setdefault("metadata", {})

            dataset_info["metadata"]["exportMappingInfo"] = upload_msg_value
            update_dataset_info(dataset_info)

    if not script_ret.valid:
        raise Exception("脚本清洗结果异常，没有数据或者json、zip都有结果")
    return script_ret
