import json
from src.utils.api_utils import get_dataset_info, update_dataset_info
from utils.data_io_utils import upload_metadata


def json_upload(json_info_mapping):
    # 全部长度
    all_total_count = 0

    # 所有的dataset信息
    all_json_dataset_infos = []

    # 过滤后的信息
    filter_dataset_infos = {}

    # dataset信息的映射
    dataset_info_mapping = {}

    # metadata的映射
    dataset_info_metdata_mapping = {}

    # 读取所有的任务

    print("开始啦", json_info_mapping)
    for json_name, json_upload_info in json_info_mapping.items():
        json_path = json_upload_info["json_path"]
        with open(json_path, "r") as f:
            json_data = json.load(f.read())

        print("json_name", json_name)
        print("data信息", json_data["data"])
        # exit()
        all_total_count += json_data["total"]

        s3_info = json_data.get("s3_info")
        meta_data = json_data.get("meta_data")

        dataset_id = json_upload_info["dataset_id"]

        dataset_metadata_info = get_dataset_info(dataset_id)

        # 如果 dataset_info 中没有 'metadata' 字段，否则初始化为一个空字典
        dataset_metadata_info.setdefault("metadata", {})

        if meta_data:
            metadata_url, upload_dir = upload_metadata(meta_data, upload_dir)
            dataset_metadata_info["metadata"]["metadataUrl"] = metadata_url

        if s3_info:

            def to_camel_case(snake_str):
                components = snake_str.split("_")
                return components[0] + "".join(x.title() for x in components[1:])

            s3_info = {to_camel_case(k.lower()): v for k, v in s3_info.items()}
            dataset_metadata_info["metadata"]["s3Info"] = s3_info
            # 更新
            update_dataset_info(dataset_metadata_info)
