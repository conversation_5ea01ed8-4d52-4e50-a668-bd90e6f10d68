from pathlib import Path
from concurrent.futures import <PERSON><PERSON>oolExecutor, as_completed, ThreadPoolExecutor

from src.config.log import logger

from src.config import THREAD_NUMBER
from src.utils.progress_utils import ProgressBar
import multiprocessing


def process_pool(
    func,
    task_list,
    *args,
    pool_num=max(1, multiprocessing.cpu_count()),
    topic=None,
    p_bar=None,
    **kwargs,
):
    error_list = []
    result_list = []
    error_task_names = set()
    with ProcessPoolExecutor(pool_num) as executor:
        futures = [executor.submit(func, task, *args, **kwargs) for task in task_list]

        for future in as_completed(futures):
            try:
                result = future.result()
            except Exception as e:
                result = None
                logger.exception(e)
                error_list.append(f"{topic}: {e}")
            else:
                if topic == "点云数据处理":
                    dataset_info, file_path, success, extra_info = result
                    if success:
                        result_list.append((dataset_info, file_path))
                    else:
                        if isinstance(extra_info, dict) and "task_name" in extra_info:
                            error_task_names.add(extra_info["task_name"])
                else:
                    if result:
                        result_list.append(result)

            if p_bar:
                key = kwargs["key"]
                number = kwargs["number"]
                total = kwargs["total"]
                p_bar.update_par(
                    "UPLOAD_PROGRESS", key, total, number=number, incremental=True
                )

                if key in ["dataprocess"]:
                    if result and not result[1].startswith("http"):
                        p_bar.file_size += (
                            Path(result[1]).stat().st_size
                        )  # 使用pathlib替换os.path.getsize
                        p_bar.file_count += 1
                        p_bar.update_state(
                            "FILE_SIZE", f"{p_bar.file_size}:{p_bar.total_file_size}"
                        )
                        p_bar.update_state(
                            "FILE_COUNT", f"{p_bar.file_count}:{p_bar.total_file_count}"
                        )

    return result_list, error_list, error_task_names


def thread_pool(
    func,
    task_list,
    *args,
    thread_num: int = THREAD_NUMBER,
    topic: str = None,
    p_bar: ProgressBar = None,
    **kwargs,
):
    error_list = []
    result_list = []
    with ThreadPoolExecutor(thread_num) as executor:
        futures = [executor.submit(func, task, *args, **kwargs) for task in task_list]

        for future in as_completed(futures):
            try:
                result = future.result()
                result_list.append(result)
            except Exception as e:
                result = None
                logger.exception(e)
                error_msg = f"{topic}: {type(e)}: {e}"

                error_list.append(error_msg)
            if p_bar:
                key = kwargs["key"]
                number = kwargs["number"]
                total = kwargs["total"]
                p_bar.update_par(
                    "UPLOAD_PROGRESS", key, total, number=number, incremental=True
                )

                if key in ["dataprocess"]:
                    if result:
                        p_bar.file_size += Path(result[1]).stat().st_size
                        p_bar.file_count += 1

                        p_bar.update_state(
                            "FILE_SIZE", f"{p_bar.file_size}:{p_bar.total_file_size}"
                        )
                        p_bar.update_state(
                            "FILE_COUNT", f"{p_bar.file_count}:{p_bar.total_file_count}"
                        )

    return result_list, error_list
