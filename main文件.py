    logger.info("查询接口")
    upload_record_info, zip_list_info = get_upload_info(app_config.upload_record_id)

    logger.info(f"查询数据信息:{zip_list_info}")

    logger.info("上传数据核查类型")
    upload_type = check_upload_mode_type(zip_list_info)
    logger.info(
        f"数据类型:{upload_type}",
    )
    p_bar.update_state("STATUS", "ING")

    upload_record_info["status"] = "ING"

    update_upload_info(upload_record_info)

    SCRIPT_CONFIG["enabled"] = True

    logger.info("开始下载数据")
    zipinfo_mapping = download_predata(
        zip_info_list=zip_list_info,
        compressed_dir=COMPRESS_DIR,
        target_dir=DATA_DIR,
        other_dir=OTHER_DIR,
        is_script_mode=SCRIPT_CONFIG.get("enabled"),
    )

    logger.info(f"数据下载完成信息:{zipinfo_mapping}")

    if SCRIPT_CONFIG.get("enabled"):
        logger.info("进入脚本")
        script_ret = handle_script(
            upload_record_info,
            zipinfo_mapping,
        )
        logger.info(f"脚本处理结果：{script_ret}")
        zipinfo_mapping = script_ret.zipinfo_script_mapping

    if upload_type == "JSON":
        print("处理json数据........................")
        json_upload(zipinfo_mapping)

    elif upload_type == "ZIP":
        ...
