def check_upload_mode_type(items: list):
    """检查上传的元素类型是不是同一个类型并返回

    Args:
        items (list): 上传的元素列表

    Returns:
        bool: True 表示所有元素类型相同，False 表示不同
    """

    if not items:
        return True

    first_type = items[0].get("upload_mode_type")

    if first_type is None:
        raise ValueError("元素缺少'upload_mode_type'键")

    for item in items[1:]:
        current_type = item.get("upload_mode_type")

        if current_type is None:
            raise ValueError("元素缺少'upload_mode_type'键")
        if current_type != first_type:
            raise ValueError(f"元素类型不一致，发现{first_type}和{current_type}")

    return first_type
