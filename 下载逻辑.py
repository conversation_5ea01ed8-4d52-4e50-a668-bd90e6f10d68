# 常量定义
SCRIPT_FILE_NUMBER = 1  # 脚本模式下的脚本文件数量
ATTACHMENT_FILE_NUMBER = 1  # 脚本模式下的附件文件数量
ATTACHMENT_COMPRESS_PACKAGE = 1  # 脚本模式下的附件文件数量


class DataProcessor:
    """数据处理类"""

    @staticmethod
    def attachment_handle(attachment_dir: Union[str, Path]) -> Dict:
        """
        解析附件文件夹，加载依赖，提取出脚本运行所需要的额外数据
        """
        DEFAULT_RUNTIME_CONFIG = {
            "enable_multiprocessing": False,
            "local_module_name": "custom_libs",
            "mirror": None,
            "data_dir": None,
        }

        attachment_dir = Path(attachment_dir)

        dependency_dir = None

        queue = deque([attachment_dir])

        while queue:
            cur = queue.popleft()
            for sub in cur.iterdir():
                if not sub.is_dir():
                    continue
                if sub.name == "dependency":
                    dependency_dir = sub
                    break
                queue.append(sub)
            if dependency_dir is not None:
                break
        if dependency_dir is None:
            raise Exception("附件文件中未找到 dependency 文件夹")
        attachment_dir = dependency_dir.parent

        # 加载脚本运行配置
        script_runtime_config = DEFAULT_RUNTIME_CONFIG

        script_runtime_config_path = attachment_dir / "config.json"
        logger.info(f"脚本配置文件:{script_runtime_config_path}")
        # 在附件目录下生成配置文件
        if script_runtime_config_path.is_file():
            with script_runtime_config_path.open("r", encoding="utf-8") as fp:
                script_runtime_config.update(json.load(fp))

        local_model_name = script_runtime_config.get("local_module_name", "custom_libs")
        mirror = script_runtime_config.get("mirror")

        if mirror is None:
            mirror = r"https://pypi.tuna.tsinghua.edu.cn/simple"
            script_runtime_config["mirror"] = mirror
        # 处理依赖安装
        logger.info("开始加载依赖")
        for wheel_path in dependency_dir.rglob("*.whl"):
            cmd = [sys.executable, "-m", "pip", "install", wheel_path]
            if mirror:
                cmd.append("-i")
                cmd.append(mirror)
            try:
                subprocess.check_call(cmd)
            except subprocess.CalledProcessError as e:
                logger.info(f"安装 {wheel_path} 失败: {e}")
        # 获取依赖的requirement
        for requirement_path in dependency_dir.rglob("requirement-pip.txt"):
            cmd = [sys.executable, "-m", "pip", "install", "-r", requirement_path]
            if mirror:
                cmd.append("-i")
                cmd.append(mirror)
            try:
                subprocess.check_call(cmd)
            except subprocess.CalledProcessError as e:
                logger.info(f"安装 {requirement_path} 失败: {e}")
        # 删除所有非 custom_libs 的文件夹
        for sub in dependency_dir.iterdir():
            if not sub.is_dir():
                continue
            if sub.name == local_model_name:
                continue
            shutil.rmtree(sub)
        sys.path.append(str(dependency_dir))
        logger.info("依赖加载完毕")

        script_runtime_config["data_dir"] = str(attachment_dir / "data")
        return script_runtime_config

    @staticmethod
    def download_predata(
        zip_info_list: List[Dict],
        compressed_dir: Optional[Union[str, Path]],
        target_dir: Optional[Union[str, Path]],
        other_dir: Optional[Union[str, Path]],
        is_script_mode: bool = False,
    ):
        """
        下载ZIP或者JSON数据或者SCRIPT数据

        Args:
            zip_info_list (list): 压缩包信息列表
            compressed_dir (Optional[Union[str, Path]], optional): 压缩包目录. Defaults to None.
            target_dir (Optional[Union[str, Path]], optional): 目标目录. Defaults to None.
            is_script_mode (bool, optional): 是否脚本模式. Defaults to False.
        """

        def init_dir(path: Union[str, Path]) -> Path:
            path = Path(path).resolve()
            path.mkdir(parents=True, exist_ok=True)
            return str(path)  # 将Path对象转换为字符串后返回

        compressed_dir = init_dir(compressed_dir)
        target_dir = init_dir(target_dir)
        other_dir = init_dir(other_dir)

        script_runtime_config = {}

        print("是否脚本模式:", is_script_mode)
        if is_script_mode:
            p_bar.down_file_count = (
                len(zip_info_list) + SCRIPT_FILE_NUMBER + ATTACHMENT_FILE_NUMBER
            )
            p_bar.zip_count = len(zip_info_list) + ATTACHMENT_COMPRESS_PACKAGE

            logger.info(f"下载脚本{SCRIPT_RUNTIME_DIR}")
            script_path = FileDownloader.download_file(
                SCRIPT_CONFIG["SCRIPT_URL"], "script", SCRIPT_RUNTIME_DIR
            )
            sys.path.append(str(Path(script_path).parent))

            logger.info("下载附件:", compressed_dir)
            attachment_path = FileDownloader.download_file(
                SCRIPT_CONFIG["ATTACHMENT_URL"], "attachment", compressed_dir
            )

            attachment_dir = CompressionHandler.decompression(
                attachment_path, compressed_dir
            )
            logger.info(f"附件文件夹路径:{attachment_path}")
            print("开始处理附件")
            script_runtime_config = DataProcessor.attachment_handle(attachment_dir)

            print("附件处理结果:", script_runtime_config)

        else:
            p_bar.zip_count = len(zip_info_list)
            p_bar.down_file_count = len(zip_info_list)

        zip_info_mapping = {}

        for zip_info in zip_info_list:
            upload_type = zip_info.get("upload_mode_type")
            file_id = zip_info.get("id")
            url = zip_info.get("url")

            if not all([upload_type, file_id, url]):
                logger.warning(f"跳过无效 zip_info：{zip_info}")
                continue

            zip_name = f"{file_id}_{Path(url).stem}"

            if upload_type == "ZIP":
                save_path = FileDownloader.download_file(url, file_id, compressed_dir)
                decompressed_file_path = CompressionHandler.decompression(
                    save_path, target_dir
                )
                save_path.unlink(missing_ok=True)
                zip_info["decompressed_file_path"] = decompressed_file_path

            elif upload_type == "JSON":
                json_file_path = FileDownloader.download_file(url, file_id, target_dir)
                zip_info["json_file_path"] = json_file_path
            else:
                FileDownloader.download_file(url, file_id, other_dir)
                zip_info["other_file_path"] = other_dir / file_id
            zip_info_mapping[zip_name] = zip_info

        return {
            "zip_info_mapping": zip_info_mapping,
            "script_runtime_config": script_runtime_config,
        }

    @staticmethod
    def upload_metadata(metadata, upload_dir):
        """
        将元数据保存到本地json上传到s3，返回上传
        """
        local_save_dir = Path(LOCAL_FILE_SAVE_DIR)

        filename = f"{UPLOAD_RECORD_ID}.json"

        metadata_path = local_save_dir / filename

        with open(metadata_path, "w", encoding="utf-8") as file:
            json.dump(metadata, fp=file, indent=4, ensure_ascii=False)
        upload_msg = {filename: metadata_path}

        metadata_url = s3_uplod_file(upload_msg, upload_dir).get(filename)

        return metadata_url, upload_dir
