import requests
from src.config.log import logger
from src.utils.decorators_utils import log_and_handle_api
from src.config import NAMESPACE


def request_data(service, path, method="get", data=None, retry=0, **kwargs):
    url: str = f"http://{service}.{NAMESPACE}.svc.cluster.local:8080{path}"
    try:
        resp = requests.request(method, url, json=data)
        if not str(resp.status_code).startswith("2") or resp.headers.get("Error"):
            raise Exception(f"请求失败, {resp.status_code}: {resp.text}")
        return True, [] if resp.status_code == 204 else resp.json()
    except Exception as e:
        logger.warning(f"{url}, 接口请求失败, 第{retry + 1}次, 请求信息: {data}")
        if retry < 3:
            return request_data(
                service, path, method=method, data=data, retry=retry + 1
            )
        logger.exception(e)
        return False, f"{url}, 接口请求失败, 重试{retry}次, error: {e}"


@log_and_handle_api("查询上传记录信息")
def get_upload_info(upload_record_id):
    ok, res = request_data("dataset", f"/v1/dataset/upload_record/{upload_record_id}")
    if not ok:
        raise Exception(f"查询上传记录信息失败: {res}")
    zip_info = [
        {
            "id": str(u["id"]),
            "url": u["url"],
            "dataset_id": res["datasetId"],
            "upload_type": res["uploadType"],
            "upload_mode_type": u["uploadMode"],
            "create_time": res["createTime"],
        }
        for u in res.get("uploadRecordUrls", [])
    ]
    return res, zip_info


@log_and_handle_api("更新上传记录状态")
def update_upload_info(info):
    ok, res = request_data(
        "dataset", "/v1/dataset/upload_record", method="put", data=info
    )
    if not ok:
        raise Exception(f"更新上传记录失败: {res}")


@log_and_handle_api("根据MD5/SHA26批量查询数据")
def get_duplicate_data_info(data):
    ok, res = request_data(
        "dataset", "/v1/dataset/basic_data/get_list", method="post", data=data
    )
    if not ok:
        raise Exception(f"查询md5/sha256重复数据信息失败: {res}")
    return res


@log_and_handle_api("保存数据到数据集")
def save_data_to_dataset(
    project_category,
    upload_record_id,
    dataset_id,
    upload_type,
    upload_mode_type,
    space_id,
    upload_time,
    user_id,
    basic_data_list,
):
    data = {
        "projectCategory": project_category,
        "uploadRecordId": upload_record_id,
        "datasetId": dataset_id,
        "uploadType": upload_type,
        "uploadModeType": upload_mode_type,
        "spaceId": space_id,
        "uploadTime": upload_time,
        "userId": user_id,
        "basicDataList": basic_data_list,
    }
    ok, res = request_data("dataset", "/v1/dataset/data/add", method="post", data=data)
    if not ok:
        raise Exception(f"保存数据到数据集失败: {res}")
    return res


@log_and_handle_api("查询数据集信息")
def get_dataset_info(dataset_id):
    ok, res = request_data("dataset", f"/v1/dataset/{dataset_id}")
    if not ok:
        raise Exception(f"查询数据集信息失败: {res}")
    return res


@log_and_handle_api("更新数据集信息")
def update_dataset_info(data):
    ok, res = request_data("dataset", "/v1/dataset", method="put", data=data)
    if not ok:
        raise Exception(f"更新数据集信息失败: {res}")


@log_and_handle_api("更新metadata")
def update_dataset_metadata(data):
    ok, res = request_data(
        "dataset", "/v1/dataset/basic_data/update/metadata", method="put", data=data
    )
    if not ok:
        raise Exception(f"更新数据集metadata信息失败: {res}")


@log_and_handle_api("动态获取签名")
def get_sign_url(url):
    try:
        resp = requests.post("http://import:8080/v1/import/sign", json=[url])
        if resp.status_code == 200:
            return resp.json()[0].get("signedUrl")
        raise Exception(f"动态获取签名失败, {resp.status_code}, {resp.text}")
    except Exception as e:
        raise Exception(f"动态获取签名失败: {e}")


@log_and_handle_api("数据集校验: 查询数据集-项目关联关系")
def get_dataset_to_project_relation(dataset_id):
    ok, res = request_data("project", f"/v1/projects/dataset/simple/{dataset_id}")
    if not ok:
        raise Exception(f"数据集校验: 查询数据集-项目关联关系失败: {res}")
    return res


@log_and_handle_api("数据集校验: 获取数据集文件数量")
def get_dataset_count(dataset_id):
    ok, res = request_data("dataset", f"/v1/dataset/data/count?dataSetId={dataset_id}")
    if not ok:
        raise Exception(f"数据集校验: 获取数据集文件数量失败: {res}")
    return res


@log_and_handle_api("数据集校验: 获取数据集目录信息")
def get_dataset_dirinfo(dataset_id, page, size):
    ok, res = request_data(
        "dataset",
        f"/v1/dataset/data/dir/top?dataSetId={dataset_id}&page={page}&size={size}",
    )
    if not ok:
        raise Exception(f"数据集校验: 获取数据集目录信息失败: {res}")
    return res


@log_and_handle_api("数据集校验: 获取数据集结构")
def get_directory_structure(dataset_id, dir_id):
    ok, res = request_data(
        "dataset", f"/v1/dataset/data/structure?dataSetId={dataset_id}&dirId={dir_id}"
    )
    if not ok:
        raise Exception(f"数据集校验: 获取数据集结构失败: {res}")
    return res


@log_and_handle_api("数据集校验: 更新数据集状态")
def update_dataset_state(data):
    ok, res = request_data("project", "/v1/projects/dataset", method="put", data=data)
    if not ok:
        raise Exception(f"数据集校验: 更新数据集状态失败: {res}")
    return res


